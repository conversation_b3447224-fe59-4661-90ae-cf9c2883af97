#!/usr/bin/env python3
"""
سكريبت لتحميل نموذج Qwen-1.8B من Hugging Face
"""

import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from huggingface_hub import snapshot_download
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_qwen_model():
    """تحميل نموذج Qwen-1.8B"""
    
    model_name = "Qwen/Qwen-1_8B"
    local_dir = "./qwen-1.8b-model"
    
    logger.info(f"بدء تحميل نموذج {model_name}")
    logger.info(f"سيتم حفظ النموذج في: {local_dir}")
    
    try:
        # إنشاء المجلد المحلي إذا لم يكن موجوداً
        os.makedirs(local_dir, exist_ok=True)
        
        # تحميل النموذج والتوكنايزر
        logger.info("تحميل التوكنايزر...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            trust_remote_code=True,
            cache_dir=local_dir
        )
        
        logger.info("تحميل النموذج...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,  # استخدام float16 لتوفير الذاكرة
            device_map="auto",
            trust_remote_code=True,
            cache_dir=local_dir
        )
        
        # حفظ النموذج والتوكنايزر محلياً
        logger.info("حفظ النموذج محلياً...")
        model.save_pretrained(local_dir)
        tokenizer.save_pretrained(local_dir)
        
        logger.info("تم تحميل النموذج بنجاح!")
        logger.info(f"النموذج محفوظ في: {os.path.abspath(local_dir)}")
        
        # اختبار بسيط للنموذج
        logger.info("اختبار النموذج...")
        test_text = "مرحبا، كيف حالك؟"
        inputs = tokenizer(test_text, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_length=50,
                num_return_sequences=1,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        logger.info(f"نص الاختبار: {test_text}")
        logger.info(f"النتيجة: {generated_text}")
        
        return True
        
    except Exception as e:
        logger.error(f"خطأ في تحميل النموذج: {str(e)}")
        return False

def download_with_git_lfs():
    """طريقة بديلة للتحميل باستخدام git lfs"""
    
    logger.info("تحميل النموذج باستخدام git lfs...")
    
    try:
        # التحقق من وجود git lfs
        import subprocess
        result = subprocess.run(["git", "lfs", "version"], capture_output=True, text=True)
        if result.returncode != 0:
            logger.error("git lfs غير مثبت. يرجى تثبيته أولاً")
            return False
        
        # استنساخ المستودع
        clone_cmd = [
            "git", "clone", 
            "https://huggingface.co/Qwen/Qwen-1_8B",
            "qwen-1.8b-git"
        ]
        
        result = subprocess.run(clone_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("تم تحميل النموذج بنجاح باستخدام git!")
            return True
        else:
            logger.error(f"خطأ في git clone: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"خطأ في التحميل باستخدام git: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    
    logger.info("=== تحميل نموذج Qwen-1.8B ===")
    logger.info("الحجم: 1.8 مليار معامل")
    logger.info("تاريخ الإصدار: نوفمبر 2023")
    logger.info("طول السياق: 32K رمز")
    logger.info("مناسب للأجهزة المتوسطة")
    logger.info("=" * 40)
    
    # محاولة التحميل باستخدام transformers أولاً
    success = download_qwen_model()
    
    if not success:
        logger.info("محاولة التحميل باستخدام git lfs...")
        success = download_with_git_lfs()
    
    if success:
        logger.info("تم تحميل النموذج بنجاح! 🎉")
        logger.info("يمكنك الآن استخدام النموذج من المجلد المحلي")
    else:
        logger.error("فشل في تحميل النموذج ❌")

if __name__ == "__main__":
    main()
